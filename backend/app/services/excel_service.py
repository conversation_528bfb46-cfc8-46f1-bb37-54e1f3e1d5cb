"""
Excel处理服务
"""

import openpyxl
import logging
from typing import Dict, List, Any
from io import BytesIO
from werkzeug.utils import secure_filename
from app.models.translation import ExcelData
from app.models.common import ApiResponse
from app.services.langchain_service import LangchainService
from app.models.common import Code

# 设置日志
logger = logging.getLogger(__name__)


class ExcelService:
    """Excel处理服务类"""

    # 允许的文件扩展名
    ALLOWED_EXTENSIONS = {"xlsx", "xls"}

    def __init__(self):
        """初始化Excel服务"""
        try:
            self.langchain_service = LangchainService()
            logger.info("LangchainService初始化成功")
        except Exception as e:
            logger.error(f"LangchainService初始化失败: {str(e)}")
            self.langchain_service = None

    def allowed_file(self, filename: str) -> bool:
        """检查文件扩展名是否允许"""
        return (
            "." in filename
            and filename.rsplit(".", 1)[1].lower() in self.ALLOWED_EXTENSIONS
        )

    def parse_excel(self, file_content: bytes, filename: str) -> ExcelData:
        """
        解析Excel文件

        Args:
            file_content: Excel文件内容
            filename: 文件名

        Returns:
            ExcelData: 解析后的数据
        """
        try:
            # 从字节流创建工作簿
            workbook = openpyxl.load_workbook(BytesIO(file_content))
            sheet = workbook.active

            # 获取所有数据
            data = []
            for row in sheet.iter_rows(values_only=True):
                if any(cell is not None for cell in row):  # 跳过空行
                    data.append([cell if cell is not None else "" for cell in row])

            if not data:
                raise ValueError("Excel文件为空")

            # 假设第一行是标题行，第一列是key，其他列是不同语言
            headers = data[0]
            if len(headers) < 2:
                raise ValueError("Excel文件格式错误：至少需要2列（key列和语言列）")

            # 提取语言列表（跳过第一列的key列）
            languages = [str(header).strip() for header in headers[1:] if header]

            # 构建翻译数据
            translationObject = {}
            for row_data in data[1:]:  # 跳过标题行
                if not row_data or not row_data[0]:  # 跳过空行或没有key的行
                    continue

                key = str(row_data[0]).strip()
                if not key:
                    continue

                # 为每个key创建语言映射
                translationObject[key] = {}
                for i, lang in enumerate(languages):
                    # i+1 因为第一列是key
                    value = row_data[i + 1] if i + 1 < len(row_data) else ""
                    translationObject[key][lang] = (
                        str(value) if value is not None else ""
                    )

            # 获取语言名称映射
            language_names = {}
            translate_status = ""
            if self.langchain_service and languages:
                try:
                    language_names = self.langchain_service.get_language_names(
                        languages
                    )
                    logger.info(f"成功获取语言名称映射: {language_names}")
                except Exception as e:
                    logger.error(f"获取语言名称失败: {str(e)}")
                    # 如果获取失败，使用默认映射
                    language_names = {lang: f"语言_{lang}" for lang in languages}
            else:
                # 如果LangchainService未初始化，使用默认映射
                language_names = {lang: f"语言_{lang}" for lang in languages}

            # 构建元数据
            metadata = {
                "filename": filename,
                "total_keys": len(translationObject),
                "total_languages": len(languages),
                "sheet_name": sheet.title,
            }

            print(language_names)

            return ExcelData(
                languages=languages,
                language_names=language_names,
                translations=translationObject,
                metadata=metadata,
            )

        except Exception as e:
            raise ValueError(f"Excel文件解析失败: {str(e)}")

    def parse_and_process_excel(
        self, filename: str, file_content: bytes, original_filename: str
    ) -> ApiResponse:
        """
        解析和处理Excel文件

        Args:
            filename: 安全的文件名
            file_content: 文件内容字节
            original_filename: 原始文件名

        Returns:
            ApiResponse: 统一响应格式
        """
        try:
            # 验证文件格式
            if not self.allowed_file(original_filename):
                return ApiResponse.error(
                    message="不支持的文件格式，请上传.xlsx或.xls文件"
                )

            # 解析Excel文件
            excel_data = self.parse_excel(file_content, filename)

            logger.info(
                f"Excel文件解析成功: {filename}, 语言数: {len(excel_data.languages)}, 键数: {len(excel_data.translations)}"
            )
            return ApiResponse.success(excel_data.dict(), "Excel文件解析成功")

        except ValueError as e:
            logger.error(f"Excel文件解析失败: {str(e)}")
            return ApiResponse.error(message=str(e))

        except Exception as e:
            logger.error(f"文件处理失败: {str(e)}")
            return ApiResponse.error(message=f"文件处理失败: {str(e)}")
